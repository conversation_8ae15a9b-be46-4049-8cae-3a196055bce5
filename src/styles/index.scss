@tailwind base;
@tailwind components;
@tailwind utilities;
@import url(./dotPulse.scss);

@layer utilities {
  .badge {
    @apply after:content-[attr(data-badge-content)] after:bg-red-500 after:text-white after:text-supertiny after:font-bold;
    @apply after:absolute after:top-0 after:right-0 after:translate-y-[-50%] after:px-1 after:py-[0.0625rem];
    @apply after:rounded-tr-md after:rounded-br-md after:rounded-tl-md;
    @apply after:border-solid after:border-white after:border;
  }
}

@layer base {
  :root {
    --installment-navbar-height: 0;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
  }
}

#installment-app-root {
  height: 100dvh;
  // overflow: hidden;
  font-size: inherit;
}

.installment-app-container {
  @apply max-w-2xl mx-auto;
  padding-bottom: var(--installment-navbar-height, 0); 
}


.stories {
  --slide-spacing: 0;
  --slide-size: 100%;
  --slide-height: 100%;
  padding: 0;
}
.stories__viewport {
  overflow: hidden;
}
.stories__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y;
  margin-left: calc(var(--slide-spacing) * -1);
}
.stories__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  position: relative;
}
.stories__slide__img {
  display: block;
  height: var(--slide-height);
  width: 100%;
  object-fit: contain;
}

.stories__slide__number {
  display: none;
}

.container-linear {
  animation: fadeInNBright 3s cubic-bezier(0.25, 1, 0.5, 1);
  overflow-y: scroll;
  scroll-behavior: smooth;
  scroll-snap-type: y mandatory;
  background-image: radial-gradient(rgba(3, 202, 119, 1), rgba(232, 232, 204, 0) 32vw), radial-gradient(rgba(0, 104, 255, 1), rgba(0, 0, 255, 0) 32vw), radial-gradient(rgba(250, 168, 40, 1), rgba(255, 0, 0, 0) 30vw), radial-gradient(rgba(0, 128, 0, 0.3), rgba(0, 128, 0, 0) 40vw), radial-gradient(rgba(255, 0, 0, 0.3), rgba(255, 0, 0, 0) 30vw);
  background-position: 34vw -15rem, -19% -2rem, 52vw 8rem, 16vw calc(14rem + -12vw), 30vw calc(14rem + 20vw);
  background-size: 80vw 80vw;
  background-repeat: no-repeat;
  position: relative;
}

@keyframes fadeInNBright {
  0% {
      filter: brightness(96%);
      background-image: radial-gradient(rgba(3, 202, 119, 1), rgba(255, 255, 0, 0) 32vw), radial-gradient(rgba(0, 104, 255, 1), rgba(0, 0, 255, 0) 32vw), radial-gradient(rgba(250, 168, 40, 1), rgba(255, 0, 0, 0) 30vw), radial-gradient(rgba(0, 128, 0, 0.3), rgba(0, 128, 0, 0) 30vw), radial-gradient(rgba(255, 0, 0, 0.3), rgba(255, 0, 0, 0) 30vw);
      background-position: -40vw 14rem, 50% 10rem, 60vw 14rem, -10vw calc(14rem + 20vw), 30vw calc(14rem + 20vw);
      background-size: 80vw 80vw;
      background-repeat: no-repeat;
 }
  100% {
      filter: brightness(100%);
 }
}

.image-fade-in {
  animation: imageBurn 2s cubic-bezier(0.25, 1, 0.5, 1);
}

@keyframes imageBurn {
  0% {
    mask: linear-gradient(90deg, #000 25%, #000000e6 50%, #00000000) 150% 0 / 400% no-repeat;
    opacity: .2;
  }
  100% {
    mask: linear-gradient(90deg, #000 25%, #000000e6 50%, #00000000) 0 / 400% no-repeat;
    opacity: 1;
  }
}


#webpack-dev-server-client-overlay {
  display: none;
}

#installment-app-root span[aria-live="polite"] {
    display: none;
}
