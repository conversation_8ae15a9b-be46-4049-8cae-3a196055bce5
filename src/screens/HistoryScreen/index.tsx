import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { Link } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>bList, Tab } from "react-aria-components";
import type { Key } from "react-aria-components";
import { format, parseISO, parse, lastDayOfMonth, endOfDay } from "date-fns";
import { Divider } from "@/components/ui/Divider";
import { images } from "@/res";
import { formatCurrency } from "@/utils/formatCurrency";
import { TransactionItem, TransactionStatus, TransactionType, getTransactionList } from "@/api/getTransaction";
import { accountStore } from "@/store/accountStore";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { Button } from "@/components/ui/Button";
import { ScreenKey } from "@/constants";
import { useTracking } from "@/hooks/useTracking";
import { HistoryScreenId, ScreenId } from "./HistoryTrackingId";
import { onboardingStore } from "@/store/onboardingStore";
import { mappingEmptyContent } from "./mappingEmptyContent";
import { EmptyHistoryCard } from "./EmptyHistoryCard";
import { TransactionSkeleton } from "./TransactionSkeleton";
import { useDebouncedCallback } from "use-debounce";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import { XIcon } from "@/components/XIcon";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import LoadingDots from "@/components/ui/LoadingDots";
import { buildPath } from "@/utils/buildPath";
import Transaction from "@/containers/transaction";

const LIMIT_TRANS = 50;
const OFFSET = 300; //px;

const HistoryScreen = () => {
  useDocumentTitle().setTitle("Lịch sử");
  const trackEvent = useTracking(ScreenId.HistoryScreenId).trackEvent;
  const isBound = accountStore.getState().isBound;
  const [transactions, setTransactions] = useState<TransactionItem[] | null>(null);
  const [transType, setTransType] = useState<Key>(TransactionType.TRANS_TYPE_UNSPECIFIED);
  const nextCursor = useRef<string>();
  const currentType = useRef<Key>();
  const selectedTo = format(new Date(), "MM/yyyy");

  const navigation = useNavigation();

  const handleClickTransactionItem = () => {
    trackEvent(HistoryScreenId.ClickDetailTransaction, { trans_cate: transType });
  };

  const handleChangeTransType = (transType: Key) => {
    trackEvent(HistoryScreenId.SwitchTab, { tab_name: transType });
    setTransType(transType);
  };

  function removeDuplicates(array: TransactionItem[]): TransactionItem[] {
    const uniqueMap = new Map<string, TransactionItem>();

    for (const item of array) {
      if (!uniqueMap.has(item.zpTransId)) {
        uniqueMap.set(item.zpTransId, item);
      }
    }

    return Array.from(uniqueMap.values());
  }

  function groupTransactionsByMonth(transactions: TransactionItem[]) {
    const groupedByMonth: { [key: string]: TransactionItem[] } = {};

    transactions.forEach((transaction) => {
      const date = parseISO(transaction.createdAt);
      const monthYear = format(date, "MM/yyyy"); // Format as "YYYY-MM"

      if (!groupedByMonth[monthYear]) {
        groupedByMonth[monthYear] = [];
      }
      groupedByMonth[monthYear].push(transaction);
    });

    return Object.entries(groupedByMonth);
  }

  const handleGetTranslist = async (transType: Key, cursor?: string) => {
    try {
      const payload = {
        account_id: accountStore.getState().accountInfo?.account_id || "",
        to_date: endOfDay(lastDayOfMonth(parse(selectedTo, "MM/yyyy", new Date()))).toISOString(),
        "pagination.limit": LIMIT_TRANS,
        "pagination.cursor": cursor,
        ...(transType !== TransactionType.TRANS_TYPE_UNSPECIFIED ? { trans_types: transType as TransactionType } : {}),
      };
      const r = await getTransactionList(payload);

      if (r?.pagination && r?.pagination?.next_cursor && r?.pagination?.has_next) {
        nextCursor.current = r.pagination.next_cursor;
      } else {
        nextCursor.current = "";
      }
      if (r.transactions) {
        return r.transactions;
      }
      currentType.current = transType;
    } catch (err) {
      toast.error("Đã có lỗi khi thực hiện yêu cầu.");
    }
  };

  const transformTransactionStatus = useCallback((status: TransactionStatus) => {
    const StatusArr: { [key in TransactionStatus]: JSX.Element } = {
      [TransactionStatus.TRANS_STATUS_SUCCESS]: <span className="text-green-500 text-tiny">Thành công</span>,
      [TransactionStatus.TRANS_STATUS_FAILED]: <span className="text-red-500 text-tiny">Thất bại</span>,
      [TransactionStatus.TRANS_STATUS_PROCESSING]: <span className="text-yellow-500 text-tiny">Đang xử lý</span>,
      [TransactionStatus.TRANS_STATUS_PENDING]: <span className="text-yellow-500 text-tiny">Đang xử lý</span>,
      [TransactionStatus.TRANS_STATUS_UNSPECIFIED]: <span className="text-gray-500 text-tiny">Không xác định</span>,
    };
    const transformStatus = StatusArr[status] || <span className="text-gray-500 text-tiny">Không xác định</span>;

    return transformStatus;
  }, []);

  const renderTransactionbyMonth = useCallback(
    (monthString: string, transaction?: TransactionItem[]) => {
      if (!monthString || !transaction || transaction.length === 0) {
        return null;
      }

      return (
        <div key={monthString} className="w-full px-4">
          <div className="bg-white rounded-lg">
            <div className="px-4 pt-4">
              <h2 className="pb-2 text-base font-bold">Tháng {monthString}</h2>
              <Divider type="dot" />
            </div>
            <div className="px-4">
              {transaction.map((trans: TransactionItem, idxTrans: number) => (
                <Link
                  viewTransition
                  onClick={handleClickTransactionItem}
                  key={idxTrans}
                  className="flex items-center justify-between gap-2 py-4 transition-transform duration-300 ease-in-out active:opacity-20 active:scale-95 active:text-primary-dark !text-primary-dark"
                  to={{ pathname: buildPath(ScreenKey.HistoryScreen), search: `?id=${trans.zpTransId}` }}>
                  <img
                    src={
                      trans.type === TransactionType.TRANS_TYPE_REPAYMENT
                        ? images.IconTransactionRePay
                        : images.IconTransactionPay
                    }
                    className="w-9 h-9"
                  />
                  <div className="flex flex-col flex-1 space-y-1">
                    <p className="text-base truncate max-w-40">{trans.remark}</p>
                    <span className="text-tiny text-dark-300">{format(trans.createdAt, "HH:mm - dd/MM/yyyy")}</span>
                  </div>
                  <div className="flex flex-col items-end justify-center space-y-1">
                    <p className="text-base font-bold text-black">
                      {trans.type === TransactionType.TRANS_TYPE_REPAYMENT ? "" : "-"}
                      {formatCurrency(Number(trans.amount))}
                    </p>
                    {transformTransactionStatus(trans.status)}
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      );
    },
    [transType]
  );

  const handleNextPage = async () => {
    const trans = await handleGetTranslist(transType, nextCursor.current);
    if (trans && transactions) {
      setTransactions(removeDuplicates(transactions.concat(trans)));
    }
  };

  const handleScroll = useDebouncedCallback((event: any) => {
    const target = event.target as HTMLDivElement;
    const isAtBottom = target.scrollTop + target.clientHeight >= target.scrollHeight - OFFSET;
    if (isAtBottom && nextCursor.current) {
      handleNextPage();
    }
  }, 100);

  useEffect(() => {
    (async () => {
      const trans = await handleGetTranslist(transType);
      if (trans) {
        setTransactions(trans);
      }
    })();
  }, [transType, selectedTo]);

  useEffect(() => {
    trackEvent(HistoryScreenId.LoadHistoryScreen, {
      account_status: isBound ? "bound" : "unbound",
    });
  }, []);

  const handleOpenTransactionDetail = useDebouncedCallback(() => {
    const id = navigation.getParam("id");
    if (!id) {
      GlobalDrawer.closeAll();
      return;
    }

    GlobalDrawer.open({
      headless: true,
      contentClassName: "h-screen max-h-screen",
      onClose: navigation.goBack,
      children: (
        <>
          <Button className="absolute bg-white rounded-full !p-4 h-auto">
            <XIcon width={16} height={16} />
          </Button>
          <Transaction zpTransID={id} />
        </>
      ),
    });
  }, 200);

  useEffect(() => {
    handleOpenTransactionDetail();
  }, [navigation]);

  const transactionlistByMonth = transactions ? groupTransactionsByMonth(transactions) : [];

  if (!isBound) {
    const onBoardingState = onboardingStore.getState().onBoardingState;
    const emptyContent = mappingEmptyContent(onBoardingState);
    return <EmptyHistoryCard {...emptyContent} />;
  }

  return (
    <>
      <section className="w-full bg-background" onScroll={handleScroll}>
        <div className="sticky top-0 left-0 z-10 w-full">
          <Tabs selectedKey={transType} onSelectionChange={handleChangeTransType}>
            <TabList className="w-full flex data-[orientation=vertical]:flex-col data-[orientation=horizontal]:flex-row data-[orientation=horizontal]:justify-between data-[orientation=horizontal]:items-center bg-white border-solid border-b border-gray-300">
              <Tab
                id={TransactionType.TRANS_TYPE_UNSPECIFIED}
                className="flex-1 text-base font-bold text-center pt-3.5 pb-3 cursor-pointer text-dark-300 focus:outline-none data-[selected]:text-primary box-border border-solid border-b-2 border-transparent data-[selected]:border-primary">
                Tất cả
              </Tab>
              <Tab
                id={TransactionType.TRANS_TYPE_PAYMENT}
                className="flex-1 text-base font-bold text-center pt-3.5 pb-3 cursor-pointer text-dark-300 focus:outline-none data-[selected]:text-primary box-border border-solid border-b-2 border-transparent data-[selected]:border-primary">
                Mua sắm
              </Tab>
              <Tab
                id={TransactionType.TRANS_TYPE_REPAYMENT}
                className="flex-1 text-base font-bold text-center pt-3.5 pb-3 cursor-pointer text-dark-300 focus:outline-none data-[selected]:text-primary box-border border-solid border-b-2 border-transparent data-[selected]:border-primary">
                Trả nợ
              </Tab>
            </TabList>
          </Tabs>
        </div>
        {transactions && transactions.length ? (
          <div
            key={transType}
            className="flex flex-col items-center gap-2 py-4">
            {transactionlistByMonth.map((item) => renderTransactionbyMonth(item[0], item[1] as TransactionItem[]))}
            <div className="flex items-center justify-center w-full p-4">
              {nextCursor.current ? <LoadingDots /> : null}
            </div>
          </div>
        ) : transactions === null ? (
          <TransactionSkeleton />
        ) : (
          <EmptyHistoryCard key={transType} />
        )}
      </section>
    </>
  );
};

export default HistoryScreen;
