import { useEffect } from "react";
import { Link } from "@/components/app-link"
import { Button } from "@/components/ui/Button";
import { ScreenKey } from "@/constants";
import { images } from "@/res";
import { buildPath } from "@/utils/buildPath";

export const EmptyHistoryCard = (props: {
  title?: string;
  description?: string;
  ctaTitle?: string;
  ctaAction?: string;
}) => {
  useEffect(() => {
    document.getElementsByTagName("body")[0].style.setProperty("overflow", "hidden");
    return () => {
      document.getElementsByTagName("body")[0].style.removeProperty("overflow");
    };
  });
  return (
    <div className="flex flex-col items-center justify-center w-full h-screen max-h-dvh">
      <section className="flex flex-col items-center justify-center w-full gap-6 animate-force-in">
        <div className="flex flex-col items-center self-stretch justify-center px-8 text-center">
          <img src={images.EmptyHistory} width={180} height={180} />
          <div className="mt-2 font-bold text-lead text-sky-950">
            {props?.title ? props?.title : "Bạn chưa có chi tiêu trả góp"}
          </div>
          <div className="mt-2 text-base text-slate-500">
            {props?.description
              ? props?.description
              : "Khám phá ưu đãi đặc quyền chỉ dành cho trả góp qua Zalopay và mua sắm thoả thích nhé"}
          </div>
        </div>
        <Link viewTransition to={buildPath(props?.ctaAction ? props?.ctaAction : ScreenKey.HomeScreen)} replace>
          <Button variant="primary">{props?.ctaTitle ? props?.ctaTitle : "Trang chủ"}</Button>
        </Link>
      </section>
    </div>
  );
};
