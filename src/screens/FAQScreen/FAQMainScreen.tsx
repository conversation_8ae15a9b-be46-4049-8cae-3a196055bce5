import { Link } from "@/components/app-link"

import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { ScreenKey } from "@/constants";
import {
  accountImage,
  cartImage,
  paymentImage,
  languageImage,
  securityImage,
  feeImage,
  accountManagementImage,
} from "./assets/icons";
import { CIMBProvider } from "../HomeScreen/components/CIMBProvider";
import { buildPath } from "@/utils/buildPath";

export type FAQ = {
  id: string;
  title: string;
  image: string;
};

const faqOptions: FAQ[] = [
  {
    id: "inst_registration_cimb",
    title: "<PERSON>ăng ký tài khoản",
    image: accountImage,
  },
  {
    id: "inst_payment_cimb",
    title: "<PERSON>a sắm trả góp",
    image: cartImage,
  },
  {
    id: "inst_repayment_cimb",
    title: "Thanh toán dư nợ",
    image: paymentImage,
  },
  {
    id: "inst_term_cimb",
    title: "<PERSON><PERSON><PERSON><PERSON> ngữ",
    image: languageImage,
  },
  {
    id: "inst_security_cimb",
    title: "An toàn bảo mật",
    image: securityImage,
  },
  {
    id: "inst_fee_interest_cimb",
    title: "Phí và lãi",
    image: feeImage,
  },
  {
    id: "inst_account_management_cimb",
    title: "Quản lý tài khoản",
    image: accountManagementImage,
  },
];

const FAQMainScreen: React.FC = () => {
  useDocumentTitle().setTitle("Câu hỏi thường gặp");

  return (
    <>
      <main className="flex flex-col justify-between w-full min-h-screen px-4 pt-4 bg-background">
        <div className="w-full p-4 flex flex-col gap-4 bg-white rounded-lg shadow-[0px_2px_12px_0px_rgba(0, 31, 62, 0.1)]">
          <h2 className="w-full font-bold text-lead text-dark-500">Chọn chủ đề bạn cần hỗ trợ</h2>
          <div className="grid grid-cols-3 gap-y-4 gap-x-3">
            {faqOptions.map((item: FAQ) => (
              <Link
                viewTransition
                key={item?.id}
                className="flex flex-col items-center gap-3"
                to={{ pathname: buildPath(ScreenKey.FAQDetailScreen), search: `?serviceTag=${item?.id}` }}>
                <img src={item?.image} className="w-6 h-6" alt={item?.title} />
                <span className="text-center font-normal text-base text-dark-500 h-9 w-[68px]">{item?.title}</span>
              </Link>
            ))}
          </div>
        </div>
        <CIMBProvider />
      </main>
    </>
  );
};

export default FAQMainScreen;
