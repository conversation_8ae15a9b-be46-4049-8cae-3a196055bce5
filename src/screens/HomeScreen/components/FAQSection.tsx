import { accountImage, cartImage, paymentImage, languageImage, questionImage } from "../../FAQScreen/assets/icons";
import { FAQ } from "../../FAQScreen/FAQMainScreen";
import { Link } from "@/components/app-link"
import { ScreenKey } from "@/constants";
import { buildPath } from "@/utils/buildPath";

const faqOptions: FAQ[] = [
  {
    id: "inst_registration_cimb",
    title: "Đăng ký tài khoản",
    image: accountImage,
  },
  {
    id: "inst_payment_cimb",
    title: "Mua sắm trả góp",
    image: cartImage,
  },
  {
    id: "inst_repayment_cimb",
    title: "Thanh toán dư nợ",
    image: paymentImage,
  },
  {
    id: "inst_term_cimb",
    title: "Thuật ngữ",
    image: languageImage,
  },
];

export const FAQSection: React.FC = () => {
  return (
    <div className="flex flex-col gap-4 px-4 pb-4">
      <h2 className="font-bold text-lead text-dark-500">Bạn cần trợ giúp?</h2>
      <div className="grid grid-cols-3 gap-y-4">
        {faqOptions.map((item: FAQ) => (
          <Link
            viewTransition
            className="flex flex-col items-center gap-2 p-2"
            key={item?.id}
            to={{ pathname: buildPath(ScreenKey.FAQDetailScreen), search: `?serviceTag=${item?.id}` }}>
            <img src={item?.image} className="w-6 h-6" alt={item?.title} />
            <span className="text-center text-base text-dark-500 h-9 w-[68px]">{item?.title}</span>
          </Link>
        ))}
        <Link viewTransition className="flex flex-col items-center gap-2 p-2" to={{ pathname: buildPath(ScreenKey.FAQMainScreen) }}>
          <img src={questionImage} className="w-6 h-6" alt="Câu hỏi thường gặp" />
          <span className="text-center text-base text-dark-500 h-9 w-[68px]">Câu hỏi thường gặp</span>
        </Link>
      </div>
    </div>
  );
};
