import { Templates } from "@/types/promotion";
import R18RewardVoucherV2 from "@/components/ModuleFederation/R18RewardVoucherV2";
import R18ProductPageSlider from "@/components/ModuleFederation/R18ProductPageSlider";
import { useTracking } from "@/hooks/useTracking";
import { HomeScreen, ScreenId } from "../HomeTrackingId";
import { accountStore } from "@/store/accountStore";
import { INSTALLMENT_PRODUCT_INVENTORY_ID, INSTALLMENT_REWARD_INVENTORY_ID } from "@/screens/OnboardingScreen/constant";

export const MerchantSection = () => {
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const installment_account_status = accountStore.getState()?.isBound ? "bound" : "unbound";

  const onLoadProductPageSlider = () => {
    trackEvent(HomeScreen.LoadMerchantComponent, {
      installment_account_status,
    });
  };

  const onLoadRewardVoucherV2 = () => {
    trackEvent(HomeScreen.LoadVoucherComponent, {
      installment_account_status,
    });
  };

  return (
    <>
      <R18RewardVoucherV2
        className="[&>div>section>div>h3]:!text-base w-full mb-3 overflow-hidden empty:hidden"
        reward_inventory_id={INSTALLMENT_REWARD_INVENTORY_ID}
        onLoaded={onLoadRewardVoucherV2}
      />
      <R18ProductPageSlider
        request={{
          inventory_id: INSTALLMENT_PRODUCT_INVENTORY_ID,
          extra_infos: {
            position: "installment_homepage",
          },
        }}
        className="[&>div>div>h3]:!text-base [&>div>div>div]:!text-base empty:hidden mx-4 mb-4 p-4 pt-4.5 bg-white rounded-extra-lg"
        hasSkeleton
        onLoaded={onLoadProductPageSlider}
        template={Templates.LIST11}
        containerPadding={16}
      />
    </>
  );
};
