import buffer from "buffer";
import { Fragment, useEffect, useRef, useState } from "react";
import { ZPI_ENV } from "..";
import { Environment } from "@/shared/environment";
import { Root } from "./Root";
import { APP_ELEMENT_ID, APP_DISPLAY_NAME, BASE_FONT_SIZE } from "@/constants";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { createGlobalStyle } from "styled-components";
import { useMountCoreApps } from "@/hooks/useMountCoreApps";
import { appStore } from "@/store/appStore";
import { getAppInfo } from "@/lib/ZalopaySDK";
import { AppConfig } from "@/shared/app-config";

global.Buffer = buffer.Buffer;

const GlobalStyles = createGlobalStyle<{ fontSize: number }>`
:root {
  --installment-font-size: ${(props) => props.fontSize}px;
  font-size: var(--installment-font-size, 16px)!important;
}
`;

export function AppWrapper() {
  useDocumentTitle().setTitle(APP_DISPLAY_NAME);
  useMountCoreApps();
  const setAppInfo = appStore.getState().setAppInfo;
  const appInfo = appStore((state) => state.appInfo);

  const fontSizeRef = useRef(BASE_FONT_SIZE);

  const [trackingParams] = useState<any>(() =>
    Object.fromEntries(new URL(window.location.href).searchParams.entries())
  );
  const environment = inferEnvironmentFromHostname();
  const appConfig = inferAppConfig();

  useEffect(() => {
    document.getElementsByTagName("body")[0].setAttribute("id", APP_ELEMENT_ID);
    return () => {
      document.getElementsByTagName("body")[0].removeAttribute("id");
    };
  }, []);

  useEffect(() => {
    if (appInfo === null) {
      (async () => {
        const result = await getAppInfo();
        result?.status === "success" && setAppInfo(result?.data);
      })();
    }
  }, [appInfo]);

  if (window.innerWidth < 375) {
    fontSizeRef.current = Math.floor((window.innerWidth / 375) * BASE_FONT_SIZE);
  }

  return (
    <Fragment>
      <GlobalStyles fontSize={fontSizeRef.current} />
      <Root
        appid={2261}
        environment={environment}
        utm_campaign={trackingParams?.utm_campaign}
        utm_source={trackingParams?.utm_source}
        appConfig={appConfig}
      />
    </Fragment>
  );
}

export default AppWrapper;

//#region
/**
 * Attempt to infer ZPI environment from the hostname.
 *
 * TODO: This is controveral, refer https://confluence.zalopay.vn/x/qYXgBg and update if needed.
 */
const inferEnvironmentFromHostname = () => {
  const env = window.__APP_INFO__?.env;
  if (env) {
    switch (env) {
      case ZPI_ENV.SANDBOX_QC:
        return Environment.QC_SANDBOX;
      case ZPI_ENV.STAGING:
        return Environment.STAGING;
      case ZPI_ENV.PRODUCTION:
        return Environment.PRODUCTION;
    }
  }
  return Environment.PRODUCTION;
};
//#endregion

const inferAppConfig = (): AppConfig => {
  const urlParams = new URLSearchParams(window.location.search);

  // Default values
  const defaultConfig: AppConfig = {
    viewTransition: false,
    hapticFeedback: false,
  };

  // Check URL query parameters and parse boolean values
  const parseBoolean = (value: string | null): boolean | undefined => {
    if (value === null) return undefined;
    return value.toLowerCase() === 'true';
  };

  const viewTransitionParam = parseBoolean(urlParams.get('viewTransition'));
  const hapticFeedbackParam = parseBoolean(urlParams.get('hapticFeedback'));
  return {
    viewTransition: viewTransitionParam !== undefined ? viewTransitionParam : defaultConfig.viewTransition,
    hapticFeedback: hapticFeedbackParam !== undefined ? hapticFeedbackParam : defaultConfig.hapticFeedback,
  };
};
