import { useEffect, useLayoutEffect, useRef } from "react";

import { GlobalDrawer, GlobalDrawerProvider } from "@/components/GlobalDrawer";
import { MaintenanceUI } from "@/components/MaintenanceUI";
import { Toaster } from "@/components/ui/Toaster";
import { closeWindow } from "@/lib/ZalopaySDK/closeWindow";
import { setEnvironment, setModuleId } from "@/shared/environment";
import { AppStore, appStore } from "@/store/appStore";
import { utmStore } from "@/store/utmStore";
import { MaintenanceType } from "@/types/maintenanceInfo";

import { Navigator } from "./Navigator";
import { OS } from "@/constants";
import { HapticProvider } from "@/providers/HapticProvider";
import { AppConfig, setAppConfig } from "@/shared/app-config";

type Props = {
  appid: number;
  environment: number;
  /**
   * @ios
   */
  moduleId?: number;
  utm_campaign?: string;
  utm_source?: string;
  utm_toast?: string;
  appConfig: AppConfig;
};

export const Root = (props: Props) => {
  const { setUtmCampaign, setUtmSource } = utmStore.getState();
  const { setMaintananceInfo } = appStore.getState() as AppStore;
  const appInfo = appStore((state) => state.appInfo);
  const maintenanceInfo = appStore((state) => state.maintenanceInfo);
  const renderCount = useRef(0);
  console.log("render", ++renderCount.current);
  useLayoutEffect(() => {
    setEnvironment(props.environment);
  }, [props.environment]);

  useLayoutEffect(() => {
    setAppConfig(props.appConfig);
  }, [props.appConfig]);

  useEffect(() => {
    setModuleId(props.moduleId);
  }, [props.moduleId]);

  useEffect(() => {
    props?.utm_campaign && setUtmCampaign(props?.utm_campaign);
    props?.utm_source && setUtmSource(props?.utm_source);
  }, [props, setUtmCampaign, setUtmSource]);

  if (maintenanceInfo) {
    if (maintenanceInfo.type === MaintenanceType.PAGE) {
      // Fullscreen maintanance page
      return <MaintenanceUI {...maintenanceInfo} className="justify-center h-screen" onClose={closeWindow} />;
    }
    GlobalDrawer.open({
      shouldScaleBackground: false,
      dismissible: true,
      children: (
        <MaintenanceUI
          {...maintenanceInfo}
          onClose={() => {
            GlobalDrawer.close();
            setMaintananceInfo(undefined);
          }}
        />
      ),
    });
  }

  return (
    <div className="text-base installment-app-container">
      {appInfo?.os !== OS.WEB ? <HapticProvider /> : null}
      <Navigator>
        <Toaster position="top-center" visibleToasts={1} offset={24} />
        <GlobalDrawerProvider />
      </Navigator>
    </div>
  );
};
