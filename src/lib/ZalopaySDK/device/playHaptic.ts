export enum PlayHapticType {
  HeavyTap = "HeavyTap",
  LightTap = "LightTap",
  Selection = "Selection",
  MediumTap = "MediumTap",
  Soft = "Soft",
  Success = "Success",
  Error = "Error",
}
export enum HapticType {
  NOTIFICATION = "notification",
  FEEDBACK = "feedback",
  SELECTION = "selection",
}
export enum HapticNotificationSubmode {
  SUCCESS = 0,
  WARNING = 1,
  ERROR = 2,
}
export enum HapticFeedbackSubmode {
  LIGHT = 0,
  MEDIUM = 1,
  HEAVY = 2,
  SOFT = 3,
  RIGID = 4,
}
export enum HapticAndroid {
  KEYBOARD_PRESS = 3,
  KEYBOARD_RELEASE = 7,
  LONG_PRESS = 0,
  NO_HAPTICS = -1,
  ERROR = 17,
}
export const HAPTIC_MAPPINGS = {
  [PlayHapticType.HeavyTap]: {
    android: { constant: HapticAndroid.LONG_PRESS },
    ios: { type: HapticType.FEEDBACK, submode: HapticFeedbackSubmode.HEAVY },
  },
  [PlayHapticType.LightTap]: {
    android: { constant: HapticAndroid.KEYBOARD_PRESS },
    ios: { type: HapticType.FEEDBACK, submode: HapticFeedbackSubmode.LIGHT },
  },
  [PlayHapticType.Selection]: {
    android: { constant: HapticAndroid.KEYBOARD_PRESS },
    ios: { type: HapticType.SELECTION },
  },
  [PlayHapticType.MediumTap]: {
    android: { constant: HapticAndroid.KEYBOARD_RELEASE },
    ios: { type: HapticType.FEEDBACK, submode: HapticFeedbackSubmode.MEDIUM },
  },
  [PlayHapticType.Soft]: {
    android: { constant: HapticAndroid.KEYBOARD_PRESS },
    ios: { type: HapticType.FEEDBACK, submode: HapticFeedbackSubmode.SOFT },
  },
  [PlayHapticType.Success]: {
    android: { constant: HapticAndroid.KEYBOARD_PRESS },
    ios: { type: HapticType.NOTIFICATION, submode: HapticNotificationSubmode.SUCCESS },
  },
  [PlayHapticType.Error]: {
    android: { constant: HapticAndroid.ERROR },
    ios: { type: HapticType.FEEDBACK, submode: HapticFeedbackSubmode.RIGID },
  },
};

export interface PlayHapticAndroidOptions {
  constant: number;
  duration?: number;
}

export type PlayHapticIOSFeedbackType = "feedback" | "notification" | "selection";

export interface PlayHapticIOSOptions {
  feedbackType: PlayHapticIOSFeedbackType;
  submode?: number;
  intensity?: number;
}

export interface PlayHapticOptions {
  type?: PlayHapticType; // Default is LightTap if not provided
  milliseconds?: number;
  android?: PlayHapticAndroidOptions;
  ios?: PlayHapticIOSOptions;
}

/**
 * Triggers device haptic feedback (vibration) on both Android and iOS platforms via the ZaloPay SDK.
 *
 * Supports predefined types (LightTap, HeavyTap, MediumTap, Selection, Success, Error) and custom haptic patterns.
 *
 * @param {PlayHapticOptions} [options] - Haptic feedback configuration options.
 * @param {PlayHapticType} [options.type] - The vibration type. Defaults to 'LightTap' if not specified.
 * @param {number} [options.milliseconds] - Duration of vibration in milliseconds (optional).
 * @param {PlayHapticAndroidOptions} [options.android] - Android-specific haptic settings (used when type is 'Custom').
 * @param {PlayHapticIOSOptions} [options.ios] - iOS-specific haptic settings (used when type is 'Custom').
 *
 * @returns {Promise<boolean>} Resolves to true if the haptic feedback was triggered successfully, false otherwise.
 *
 * @example
 * // Trigger a default LightTap haptic
 * await playHaptic({ type: PlayHapticType.LightTap, milliseconds: 300 });
 *
 * @example
 * // Trigger a custom haptic pattern
 * await playHaptic({
 *   type: PlayHapticType.Custom,
 *   android: { constant: 0 },
 *   ios: { feedbackType: 'feedback', submode: 2, intensity: 0.8 }
 * });
 */
export const playHaptic = async (options: PlayHapticOptions = {}): Promise<boolean> => {
  try {
    await window.zlpSdk?.Device?.playHaptic({ type: 'custom', ...options});
    return true;
  } catch {
    return false;
  }
};
