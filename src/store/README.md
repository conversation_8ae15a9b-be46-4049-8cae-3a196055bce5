# History Store

The `historyStore` is a centralized state management solution for handling transaction history with caching and synchronization capabilities.

## Features

- **Persistent Caching**: Transactions are cached in IndexedDB for offline access
- **Smart Sync**: Automatically detects and syncs new transactions
- **Pagination**: Handles infinite scrolling with cursor-based pagination
- **Filtering**: Supports transaction type filtering (All, Payment, Repayment)
- **Deduplication**: Automatically removes duplicate transactions
- **Month Grouping**: Groups transactions by month for display
- **Auto-refresh**: Configurable cache expiry and auto-sync on app focus

## Usage

### Basic Usage with Hook

```tsx
import { useHistoryStore } from '@/hooks/useHistoryStore';
import { TransactionType } from '@/api/getTransaction';

const MyComponent = () => {
  const {
    transactions,
    currentTransType,
    isLoading,
    isLoadingMore,
    hasNext,
    changeTransactionType,
    loadMoreTransactions,
    getGroupedTransactions,
    transformTransactionStatus,
  } = useHistoryStore();

  // Load transactions for a specific type
  const handleTabChange = (type: TransactionType) => {
    changeTransactionType(type);
  };

  // Load more transactions (pagination)
  const handleLoadMore = () => {
    if (hasNext && !isLoadingMore) {
      loadMoreTransactions();
    }
  };

  // Get grouped transactions for display
  const groupedTransactions = getGroupedTransactions();

  return (
    <div>
      {isLoading ? (
        <div>Loading...</div>
      ) : (
        Object.entries(groupedTransactions).map(([month, transactions]) => (
          <div key={month}>
            <h3>Month {month}</h3>
            {transactions.map(transaction => (
              <div key={transaction.zpTransId}>
                {transaction.remark} - {transaction.amount}
                {transformTransactionStatus(transaction.status)}
              </div>
            ))}
          </div>
        ))
      )}
    </div>
  );
};
```

### Direct Store Usage

```tsx
import { historyStore } from '@/store/historyStore';
import { TransactionType } from '@/api/getTransaction';

// Get current state
const state = historyStore.getState();

// Load transactions
await state.loadTransactions(TransactionType.TRANS_TYPE_PAYMENT);

// Load more transactions
await state.loadMoreTransactions();

// Sync new transactions
await state.syncNewTransactions();

// Change transaction type
state.setTransType(TransactionType.TRANS_TYPE_REPAYMENT);

// Clear cache
state.clearCache();

// Reset store
state.resetStore();
```

### Auto-sync Setup

```tsx
import { useHistoryStore } from '@/hooks/useHistoryStore';

const MyComponent = () => {
  const { enableAutoSync } = useHistoryStore();

  useEffect(() => {
    // Enable auto-sync on app focus
    const cleanup = enableAutoSync();
    return cleanup; // Cleanup on unmount
  }, [enableAutoSync]);

  return <div>My Component</div>;
};
```

## Store Structure

```typescript
type HistoryStore = {
  // State
  transactions: TransactionItem[] | null;
  transactionCache: TransactionCache;
  currentTransType: TransactionType;
  selectedMonth: string;
  nextCursor?: string;
  hasNext: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  lastSyncTime?: string;

  // Actions
  setTransType: (transType: TransactionType) => void;
  setSelectedMonth: (month: string) => void;
  loadTransactions: (transType?: TransactionType, refresh?: boolean) => Promise<void>;
  loadMoreTransactions: () => Promise<void>;
  syncNewTransactions: () => Promise<void>;
  clearCache: () => void;
  resetStore: () => void;

  // Helper methods
  getGroupedTransactions: () => GroupedTransactions;
  getCacheKey: (transType: TransactionType, month: string) => string;
  removeDuplicates: (transactions: TransactionItem[]) => TransactionItem[];
  groupTransactionsByMonth: (transactions: TransactionItem[]) => [string, TransactionItem[]][];
};
```

## Cache Management

The store automatically manages cache with the following features:

- **Cache Key**: Combines transaction type and selected month
- **Expiry**: Cache expires after 1 hour by default
- **Persistence**: Uses IndexedDB for persistent storage
- **Deduplication**: Automatically removes duplicate transactions

## Sync Strategy

1. **Initial Load**: Loads transactions from cache if available and not expired
2. **Refresh**: Forces fresh data fetch from API
3. **Auto-sync**: Syncs new transactions since last update
4. **Focus Sync**: Automatically syncs when app regains focus (if > 5 minutes since last sync)

## Error Handling

- Network errors are caught and display user-friendly toast messages
- Failed requests don't clear existing cached data
- Loading states are properly managed during async operations

## Testing

The store includes comprehensive tests covering:
- Transaction loading and caching
- Pagination and infinite scroll
- Deduplication logic
- Month grouping functionality
- Error scenarios

Run tests with:
```bash
npm test src/store/__tests__/historyStore.test.ts
```

## Migration from Component State

When migrating from component-level state management:

1. Replace `useState` for transactions with `useHistoryStore()`
2. Remove manual API calls and use store methods
3. Replace custom pagination logic with `loadMoreTransactions()`
4. Use store's grouping methods instead of custom implementations
5. Remove manual caching logic

## Performance Considerations

- Transactions are cached per transaction type and month
- Deduplication is performed on every data update
- Large transaction lists are handled efficiently with pagination
- IndexedDB provides fast persistent storage
